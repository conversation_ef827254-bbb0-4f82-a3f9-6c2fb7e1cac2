<template>
  <div
    v-show="isVisible"
    :class="[
      'top-loading-bar',
      {
        'top-loading-bar--loading': isLoading,
        'top-loading-bar--complete': isComplete,
        'top-loading-bar--error': isError
      }
    ]"
  >
    <div
      class="top-loading-bar__progress"
      :style="{ width: progress + '%' }"
    ></div>
  </div>
</template>

<script setup lang="ts">
interface LoadingBarState {
  isVisible: boolean;
  isLoading: boolean;
  isComplete: boolean;
  isError: boolean;
  progress: number;
}

const state = reactive<LoadingBarState>({
  isVisible: false,
  isLoading: false,
  isComplete: false,
  isError: false,
  progress: 0
});

const { isVisible, isLoading, isComplete, isError, progress } = toRefs(state);

let progressTimer: NodeJS.Timeout | null = null;
let hideTimer: NodeJS.Timeout | null = null;

// 开始loading
const start = () => {
  reset();
  state.isVisible = true;
  state.isLoading = true;
  state.progress = 0;

  // 模拟进度增长，但不循环
  progressTimer = setInterval(() => {
    if (state.progress < 85) {
      const increment = Math.random() * 8 + 2; // 2-10的随机增量
      state.progress = Math.min(state.progress + increment, 85);
    } else {
      // 到达85%后停止自动增长，等待手动完成
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
      }
    }
  }, 300);
};

// 完成loading
const finish = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }

  state.progress = 100;
  state.isLoading = false;
  state.isComplete = true;

  // 延迟隐藏
  hideTimer = setTimeout(() => {
    hide();
  }, 300);
};

// 错误状态
const error = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }

  state.isLoading = false;
  state.isError = true;

  // 延迟隐藏
  hideTimer = setTimeout(() => {
    hide();
  }, 1000);
};

// 隐藏loading条
const hide = () => {
  state.isVisible = false;
  setTimeout(() => {
    reset();
  }, 300);
};

// 重置状态
const reset = () => {
  if (progressTimer) {
    clearInterval(progressTimer);
    progressTimer = null;
  }
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }

  state.isLoading = false;
  state.isComplete = false;
  state.isError = false;
  state.progress = 0;
};

// 暴露方法给父组件
defineExpose({
  start,
  finish,
  error,
  hide,
  reset
});

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  reset();
});
</script>

<style lang="scss" scoped>
.top-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  height: 3px;
  background-color: transparent;
  transition: opacity 0.3s ease;

  &__progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b35 0%, #f7931e 50%, #ffb347 100%);
    transition: width 0.3s ease;
    border-radius: 0 2px 2px 0;
    box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
  }

  &--loading {
    .top-loading-bar__progress {
      background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
      animation: loading-shimmer 2s infinite;
    }
  }

  &--complete {
    .top-loading-bar__progress {
      background: #f7931e;
      transition: width 0.1s ease;
    }
  }

  &--error {
    .top-loading-bar__progress {
      background: #f56c6c;
    }
  }
}

@keyframes loading-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 透明橙色主题色 */
.top-loading-bar {
  &__progress {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.9) 0%, rgba(247, 147, 30, 0.9) 50%, rgba(255, 179, 71, 0.9) 100%);
  }

  &--loading {
    .top-loading-bar__progress {
      background: linear-gradient(90deg, rgba(255, 107, 53, 0.9) 0%, rgba(247, 147, 30, 0.9) 100%);
    }
  }

  &--complete {
    .top-loading-bar__progress {
      background: rgba(247, 147, 30, 0.9);
    }
  }

  &--error {
    .top-loading-bar__progress {
      background: rgba(245, 108, 108, 0.8);
    }
  }
}
</style>